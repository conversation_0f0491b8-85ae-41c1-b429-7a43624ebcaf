.admin-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1.5rem;
  padding: 1rem 0;
  border-top: 1px solid #e5e7eb;
  gap: 1rem;
}

.admin-pagination__info {
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
}

.admin-pagination__controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.admin-pagination__btn {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 2.5rem;
  height: 2.5rem;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  background-color: #ffffff;
  color: #374151;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  user-select: none;
}

.admin-pagination__btn:hover:not(:disabled) {
  background-color: #f9fafb;
  border-color: #9ca3af;
  color: var(--secondary-color);
}

.admin-pagination__btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #f9fafb;
  color: #9ca3af;
}

.admin-pagination__btn--prev,
.admin-pagination__btn--next {
  padding: 0.5rem 1rem;
  font-weight: 600;
}

.admin-pagination__btn--page {
  min-width: 2.5rem;
}

.admin-pagination__btn--active {
  background-color: #3b82f6;
  border-color: #3b82f6;
  color: #ffffff;
}

.admin-pagination__btn--active:hover:not(:disabled) {
  background-color: #2563eb;
  border-color: #2563eb;
  color: #ffffff;
}

.admin-pagination__ellipsis {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 2.5rem;
  height: 2.5rem;
  color: #9ca3af;
  font-size: 0.875rem;
  user-select: none;
}

.admin-pagination__current {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 2.5rem;
  height: 2.5rem;
  padding: 0.5rem;
  background-color: #3b82f6;
  color: #ffffff;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 600;
}

/* Responsive design */
@media (max-width: 768px) {
  .admin-pagination {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .admin-pagination__info {
    text-align: center;
  }

  .admin-pagination__controls {
    justify-content: center;
    flex-wrap: wrap;
  }

  .admin-pagination__btn--prev,
  .admin-pagination__btn--next {
    flex: 1;
    min-width: 100px;
  }
}

@media (max-width: 480px) {
  .admin-pagination__controls {
    gap: 0.25rem;
  }

  .admin-pagination__btn {
    min-width: 2rem;
    height: 2rem;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }

  .admin-pagination__btn--prev,
  .admin-pagination__btn--next {
    padding: 0.25rem 0.75rem;
  }
}

/* Loading state */
.admin-pagination--loading .admin-pagination__btn {
  opacity: 0.6;
  cursor: wait;
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .admin-pagination {
    border-top-color: #374151;
  }

  .admin-pagination__info {
    color: #9ca3af;
  }

  .admin-pagination__btn {
    background-color: #1f2937;
    border-color: #374151;
    color: #d1d5db;
  }

  .admin-pagination__btn:hover:not(:disabled) {
    background-color: #374151;
    border-color: #4b5563;
    color: #f9fafb;
  }

  .admin-pagination__btn:disabled {
    background-color: var(--secondary-color);
    color: #6b7280;
  }

  .admin-pagination__ellipsis {
    color: #6b7280;
  }
}
