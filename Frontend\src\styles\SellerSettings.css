/* Seller Settings Styles - Updated for SellerLayout Integration */
.seller-settings-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding: 0;
  background: transparent;
  min-height: auto;
}

.seller-settings-content .settings-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
}

.seller-settings-content .section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.seller-settings-content .section-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.seller-settings-content .section-title h2 {
  font-size: var(--heading4);
  font-weight: 600;
  color: var(--secondary-color);
  margin: 0;
}

.seller-settings-content .section-icon {
  font-size: 1.5rem;
  color: var(--btn-color);
}

.seller-settings-content .refresh-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
}

.seller-settings-content .refresh-btn .spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Stripe Status Card */
.seller-settings-content .stripe-status-card {
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  overflow: hidden;
}

.seller-settings-content .loading-state,
.seller-settings-content .error-state,
.seller-settings-content .not-connected-state,
.seller-settings-content .connected-state {
  padding: 2rem;
}

.seller-settings-content .loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  text-align: center;
}

.seller-settings-content .spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid var(--btn-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.seller-settings-content .error-state {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.seller-settings-content .error-icon {
  font-size: 2rem;
  color: #e53e3e;
  flex-shrink: 0;
}

.seller-settings-content .error-content h3 {
  color: #e53e3e;
  margin: 0 0 0.5rem 0;
}

.seller-settings-content .error-content p {
  color: #718096;
  margin: 0 0 1rem 0;
}

.seller-settings-content .not-connected-state {
  text-align: center;
}

.seller-settings-content .setup-prompt h3 {
  font-size: 1.5rem;
  color: #2d3748;
  margin-bottom: 1rem;
}

.seller-settings-content .setup-prompt p {
  color: #718096;
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

.seller-settings-content .benefits-list {
  display: grid;
  gap: 1rem;
  margin: 2rem 0;
  text-align: left;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.seller-settings-content .benefit-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.seller-settings-content .benefit-icon {
  color: #48bb78;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.seller-settings-content .benefit-item span {
  color: #4a5568;
  font-weight: 500;
}

.seller-settings-content .btn-stripe-connect {
  background: #635bff;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.seller-settings-content .btn-stripe-connect:hover {
  background: var(--second-primary-color);
  transform: translateY(-1px);
}

.seller-settings-content .btn-stripe-connect:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Connected State */
.seller-settings-content .connected-state {
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
}

.seller-settings-content .status-overview {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.seller-settings-content .status-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.seller-settings-content .main-status-icon {
  font-size: 2.5rem;
}

.seller-settings-content .status-header div h3 {
  color: #2d3748;
  margin: 0 0 0.25rem 0;
}

.seller-settings-content .status-header div p {
  color: #718096;
  margin: 0;
  font-family: monospace;
  font-size: 0.9rem;
}

.seller-settings-content .status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.seller-settings-content .status-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.seller-settings-content .status-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.seller-settings-content .status-icon.success {
  color: #48bb78;
}

.seller-settings-content .status-icon.error {
  color: #e53e3e;
}

.seller-settings-content .status-content h4 {
  color: #2d3748;
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
}

.seller-settings-content .status-content p {
  color: #718096;
  margin: 0;
  font-size: 0.9rem;
}

.seller-settings-content .incomplete-warning {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  margin-top: 16px;
}

.seller-settings-content .warning-icon {
  color: #856404;
  font-size: 20px;
  margin-top: 2px;
}

.seller-settings-content .warning-content h4 {
  margin: 0 0 4px 0;
  color: #856404;
  font-size: 14px;
  font-weight: 600;
}

.seller-settings-content .warning-content p {
  margin: 0 0 12px 0;
  color: #856404;
  font-size: 13px;
}

/* Payment Info Grid */
.seller-settings-content .payment-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.seller-settings-content .info-card {
  text-align: center;
  padding: 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  background: linear-gradient(135deg, #ffffff 0%, #f7fafc 100%);
  transition: all 0.2s;
}

.seller-settings-content .info-card:hover {
  transform: scale(1.02);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.seller-settings-content .info-card h3 {
  color: #4a5568;
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.seller-settings-content .fee-display {
  font-size: var(--heading3);
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 0.5rem;
}

.seller-settings-content .info-card p {
  color: #718096;
  margin: 0;
  font-size: 0.9rem;
}

/* Help Section */
.seller-settings-content .help-card {
  text-align: center;
  padding: 1rem;
  background: linear-gradient(135deg, #edf2f7 0%, #e2e8f0 100%);
  border-radius: 12px;
  border: 1px solid #cbd5e0;
}

.seller-settings-content .help-card h3 {
  color: #2d3748;
  margin: 0 0 1rem 0;
  font-size: 1.25rem;
}

.seller-settings-content .help-card p {
  color: #718096;
  margin: 0 0 2rem 0;
}

.seller-settings-content .help-links {
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.seller-settings-content .help-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--btn-color);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s;
}

.seller-settings-content .help-link:hover {
  color: #3182ce;
}

/* Button Styles */
.seller-settings-content .btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  border: none;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.seller-settings-content .btn-primary {
  background-color: var(--btn-color);
  color: white;
}

.seller-settings-content .btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}
/* Dashboard Access Styles */
.seller-settings-content .dashboard-access {
  display: grid;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
  border: 1px solid #d4e6ff;
  border-radius: 8px;
  margin-top: 16px;
  justify-items: start;
  gap: 1rem;
}

.seller-settings-content .dashboard-info {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  flex: 1;
}

.seller-settings-content .dashboard-icon {
  color: var(--primary-color);
  font-size: 24px;
  margin-top: 2px;
}

.seller-settings-content .dashboard-info h4 {
  margin: 0 0 4px 0;
  color: var(--text-color);
  font-size: 16px;
  font-weight: 600;
}

.seller-settings-content .dashboard-info p {
  margin: 0;
  color: var(--text-muted);
  font-size: 14px;
  line-height: 1.4;
}

.seller-settings-content .dashboard-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  white-space: nowrap;
  font-size: 14px;
  font-weight: 500;
}

.seller-settings-content .dashboard-btn svg {
  font-size: 16px;
}
/* Responsive Design */
@media (max-width: 768px) {
  .seller-settings-content .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .seller-settings-content .status-grid {
    grid-template-columns: 1fr;
  }

  .seller-settings-content .payment-info-grid {
    grid-template-columns: 1fr;
  }

  .seller-settings-content .help-links {
    flex-direction: column;
    gap: 1rem;
  }



  .seller-settings-content .benefits-list {
    text-align: center;
  }
}

@media (max-width: 450px) {
  .seller-settings-content .settings-section,
  .seller-settings-content .status-header,
  .seller-settings-content .status-item,
  .seller-settings-content .dashboard-access {
    padding: 1rem;
  }
  .seller-settings-content .connected-state {
    padding: 0rem;
    border: none;
    background: transparent;
  }
  .seller-settings-content .stripe-status-card {
    border: none;
    border-radius: 0px;
    overflow: auto;
  }
}

@media (max-width: 300px) {
  .seller-settings-content .dashboard-btn svg {
    display: none;
  }
  .seller-settings-content .dashboard-access {
    padding: 0.5rem;
  }
}

/* Fee Breakdown Example Styles */
.seller-settings-content .fee-breakdown-example {
  margin-top: 24px;
  padding: 20px;
  background-color: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.seller-settings-content .fee-breakdown-example h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
}

.seller-settings-content .breakdown-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.seller-settings-content .breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.seller-settings-content .breakdown-item.deduction .value {
  color: #dc2626;
  font-weight: 500;
}

.seller-settings-content .breakdown-item.total {
  padding-top: 12px;
  border-top: 2px solid #facc15;
  margin-top: 8px;
  font-weight: 600;
}

.seller-settings-content .breakdown-item.total .value {
  color: #059669;
  font-size: 1.125rem;
  font-weight: 600;
}

.seller-settings-content .breakdown-item .label {
  font-size: 0.95rem;
  color: #374151;
}

.seller-settings-content .breakdown-item .value {
  font-size: 1rem;
  font-weight: 500;
  color: #1f2937;
}

.seller-settings-content .breakdown-note {
  margin-top: 16px;
  padding: 12px;
  background-color: #fef3c7;
  border-radius: 6px;
  border: 1px solid #fbbf24;
}

.seller-settings-content .breakdown-note small {
  color: #92400e;
  font-size: 0.875rem;
  line-height: 1.4;
}
