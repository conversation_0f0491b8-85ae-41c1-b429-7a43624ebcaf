/* BuyerRequests Component Styles - Consolidated from BuyerRequests.css and BuyerRequestsStandardized.css */
/* Scoped to BuyerRequests to avoid global conflicts */

.BuyerRequests {
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* Standardized Table Container */
.BuyerRequests .table-container {
  background: var(--white);
  border-radius: var(--border-radius-large);
  overflow-x: auto;
  box-shadow: var(--box-shadow-light);
  border: 1px solid var(--light-gray);
}

/* Standardized Table Styling - Following AdminCMSPages pattern */
.BuyerRequests .table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--smallfont);
  background-color: var(--white);
  margin: 0;
}

.BuyerRequests .table th {
  padding: var(--basefont);
  text-align: left;
  vertical-align: middle;
  background-color: var(--bg-gray);
  font-weight: 600;
  color: var(--secondary-color);
  font-size: var(--smallfont);
  border-bottom: 1px solid var(--light-gray);
  white-space: nowrap;
}

.BuyerRequests .table td {
  padding: var(--basefont);
  text-align: left;
  vertical-align: middle;
  border-bottom: 1px solid var(--light-gray);
  font-size: var(--smallfont);
  color: var(--text-color);
}

.BuyerRequests .table tbody tr:last-child td {
  border-bottom: none;
}

.BuyerRequests .content-item {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.BuyerRequests .content-image {
  width: 50px;
  height: 50px;
  border-radius: var(--border-radius);
  overflow: hidden;
  flex-shrink: 0;
}

.BuyerRequests .content-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.BuyerRequests .content-info {
  display: flex;
  flex-direction: column;
  text-align: left;
}

.BuyerRequests .content-title {
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.BuyerRequests .content-coach {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

/* Status Cell - Enhanced from Standardized version */
.BuyerRequests .status-badge {
  display: flex ;
  gap: var(--extrasmallfont);
  align-items: center;
  padding: 4px var(--smallfont);
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  font-weight: 600;
  text-transform: capitalize;
}

.BuyerRequests .status-badge.pending {
  background-color: #fef3c7;
  color: #92400e;
}

.BuyerRequests .status-badge.in-progress {
  background-color: #dbeafe;
  color: #1e40af;
}

.BuyerRequests .status-badge.completed {
  background-color: #dcfce7;
  color: #166534;
}

.BuyerRequests .status-badge.cancelled {
  background-color: #fee2e2;
  color: #dc2626;
}

/* Legacy status classes for backward compatibility */
.BuyerRequests .status-badge.approved {
  background-color: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

/* Action Buttons - Standardized Pattern */
.BuyerRequests .action-buttons {
  display: flex;
  gap: var(--smallfont);
  align-items: center;
  justify-content: center;
}

.BuyerRequests .action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: transparent;
  color: var(--text-color);
}

.BuyerRequests .action-btn:hover {
 
  transform: scale(1.05);
}

/* Empty State - Enhanced from Standardized version */
.BuyerRequests .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--heading3);
  text-align: center;
  color: var(--dark-gray);
}

.BuyerRequests .empty-state h3 {
  margin-bottom: var(--smallfont);
  color: var(--secondary-color);
  font-size: var(--heading6);
}

.BuyerRequests .empty-state p {
  color: var(--dark-gray);
  font-size: var(--smallfont);
  max-width: 400px;
}

/* Legacy empty state for backward compatibility */
.BuyerRequests__empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--heading2);
  text-align: center;
  background: var(--primary-light-color);
  border-radius: var(--border-radius-large);
  border: 2px dashed var(--light-gray);
}

.BuyerRequests__empty h3 {
  color: var(--text-color);
  font-size: var(--heading5);
  margin: 0 0 12px 0;
  font-weight: 600;
}

.BuyerRequests__empty p {
  font-size: var(--basefont);
  color: var(--dark-gray);
}

/* Filters Section - Enhanced from Standardized version */
.BuyerRequests .requests-filters {
  display: flex;
  gap: var(--basefont);
  align-items: center;
  padding: var(--basefont);
  background: var(--white);
  border-radius: var(--border-radius-large);
  margin-bottom: var(--heading6);
  box-shadow: var(--box-shadow-light);
  border: 1px solid var(--light-gray);
  flex-wrap: wrap;
}

.BuyerRequests .filter-group {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.BuyerRequests .filter-group label {
  font-size: var(--smallfont);
  color: var(--secondary-color);
  font-weight: 500;
}

.BuyerRequests .search-group {
  display: flex;
  flex-direction: column;
  gap: var(--extrasmallfont);
}

.BuyerRequests .filter-select,
.BuyerRequests .search-input {
  padding: var(--smallfont) var(--basefont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  background: var(--white);
  color: var(--text-color);
  transition: all 0.3s ease;
}

.BuyerRequests .filter-select:focus,
.BuyerRequests .search-input:focus {
  outline: none;
  border-color: var(--primary-color);

}

.BuyerRequests .search-input {
  min-width: 200px;
}

/* Stats Section - Enhanced from Standardized version */
.BuyerRequests .requests-stats {
  display: flex;
  gap: var(--basefont);
  margin-bottom: var(--heading6);
  flex-wrap: wrap;
}

.BuyerRequests .stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 5px var(--basefont);
  background: var(--white);
  border-radius: var(--border-radius-large);
  min-width: 150px;
  box-shadow: var(--box-shadow-light);
  border: 1px solid var(--light-gray);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.BuyerRequests .stat-item:hover {
  transform: scale(1.02);
  box-shadow: var(--box-shadow);
}

.BuyerRequests .stat-icon {
  font-size: var(--heading5);
  margin-bottom: var(--smallfont);
  color: var(--primary-color);
}

.BuyerRequests .stat-count {
  font-size: var(--heading4);
  font-weight: 700;
  color: var(--secondary-color);

}

.BuyerRequests .stat-label {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
  text-align: center;
  font-weight: 500;
}

/* Request Details Cell - Enhanced from Standardized version */
.BuyerRequests .request-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.BuyerRequests .request-title {
  font-weight: 600;
  color: var(--secondary-color);
  font-size: var(--smallfont);
  margin-bottom: 0px ;
}

.BuyerRequests .request-meta {
  display: flex;
  gap: var(--smallfont);
  align-items: center;
}

.BuyerRequests .content-type,
.BuyerRequests .sport {
  font-size: var(--extrasmallfont);
  padding: 2px var(--smallfont);
  border-radius: var(--border-radius);
  background: var(--bg-gray);
  color: var(--dark-gray);
}

.BuyerRequests .request-description {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  line-height: 1.4;
      text-overflow: ellipsis;
    overflow: hidden;
    width: 200px;


}

/* Seller Info Cell - Enhanced from Standardized version */
.BuyerRequests .seller-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.BuyerRequests .seller-name {
  font-weight: 500;
  color: var(--secondary-color);
  font-size: var(--smallfont);
}

.BuyerRequests .seller-email {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

/* Budget Cell - Enhanced from Standardized version */
.BuyerRequests .budget-info {
  display: flex;
  align-items: center;
}

.BuyerRequests .budget-amount {
  font-weight: 600;
  color: var(--primary-color);
  font-size: var(--smallfont);
}

.BuyerRequests .seller-price {
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

/* Date Cell */
.BuyerRequests .date-info {
  font-size: var(--smallfont);
  color: var(--text-color);
}

/* Status badge variations - Using consistent color scheme */
.BuyerRequests .status-orange {
  background: rgba(243, 156, 18, 0.1);
  color: #f39c12;
}

.BuyerRequests .status-green {
  background: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.BuyerRequests .status-red {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.BuyerRequests .status-blue {
  background: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.BuyerRequests .status-purple {
  background: rgba(155, 89, 182, 0.1);
  color: #9b59b6;
}

.BuyerRequests .status-gray {
  background: var(--bg-gray);
  color: var(--dark-gray);
}

/* Specific action button styles - Enhanced from Standardized version */
.BuyerRequests .view-btn {
 background-color: transparent;
    font-size: var(--heading6);
    color: var(--text-color);
}

.BuyerRequests .view-btn:hover {
 
  transform: scale(1.05);
}

.BuyerRequests .payment-btn {
  background: #2c5aa0;
  color: var(--white);
}

.BuyerRequests .payment-btn:hover {

  transform: scale(1.05);
}

.BuyerRequests .empty-icon {
  font-size: var(--heading2);
  color: var(--light-gray);
  margin-bottom: var(--basefont);
}

/* Retry Button - From Standardized version */
.BuyerRequests .retry-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--smallfont) var(--basefont);
  background: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  cursor: pointer;
  transition: all 0.3s ease;
}

/* Loading Skeleton - From Standardized version */
.BuyerRequests .table-skeleton {
  padding: var(--basefont);
}

/* Error Display - From Standardized version */
.BuyerRequests .error-display {
  padding: var(--basefont);
  background: #fee2e2;
  color: #dc2626;
  border-radius: var(--border-radius);
  margin-bottom: var(--basefont);
}

/* Responsive styles - Following consistent dashboard pattern */
@media (max-width: 1024px) {
  .BuyerRequests .requests-filters {
    flex-direction: column;
    align-items: stretch;
    gap: var(--smallfont);
  }

  .BuyerRequests .content-title {
    max-width: 150px;
  }
}

@media (max-width: 768px) {
  .BuyerRequests .requests-filters {
    flex-direction: column;
    align-items: stretch;
    gap: var(--basefont);
  }

  .BuyerRequests .filter-group {
    justify-content: space-between;
  }

  .BuyerRequests .search-input {
    min-width: auto;
    width: 100%;
  }

  .BuyerRequests .requests-stats {

    flex-wrap: wrap;
    gap: var(--smallfont);
  }

  .BuyerRequests .stat-item {
    min-width: 120px;
    padding: 5px var(--smallfont);
  }

  .BuyerRequests .table {
    font-size: var(--extrasmallfont);
  }

  .BuyerRequests .table th,
  .BuyerRequests .table td {
    padding: var(--smallfont);
  }

  .BuyerRequests .action-buttons {
    gap: 2px;
  }

  .BuyerRequests .action-btn {
    width: 28px;
    height: 28px;
    font-size: var(--extrasmallfont);
  }

  .BuyerRequests .content-title {
    max-width: 120px;
  }
}

@media (max-width: 480px) {
 .BuyerRequests .table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }


  .BuyerRequests .table {
    min-width: 600px;
  }

  .BuyerRequests .requests-filters {
    padding: var(--smallfont);
  }

  .BuyerRequests .stat-item {
    min-width: 70px;
 
  }

  .BuyerRequests .stat-count {
    font-size: var(--heading5);
  }

  .BuyerRequests .empty-state {
    padding: var(--heading5);
  }
}
