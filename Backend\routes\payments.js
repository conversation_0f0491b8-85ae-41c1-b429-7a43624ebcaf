const express = require('express');
const { check } = require('express-validator');
const {
  getPayments,
  getPayment,
  createPaymentIntent,
  confirmPayment,
  getBuyerPayments,
  getSellerPayments,
  processPayout,
  createConnectAccount,
  getConnectAccountStatus,
  createDashboardLink,
  updateConnectAccount,
  getConnectAccountDetails,
  createBidPaymentIntent,
  completeBidPurchase,
  manualCompletePayment
} = require('../controllers/payments');

const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// Public routes - webhook is handled directly in server.js before body parser

// Protected routes
router.use(protect);

// Buyer routes
router.get('/buyer', authorize('buyer', 'admin'), getBuyerPayments);

router.post(
  '/create-intent',
  authorize('buyer', 'admin'),
  [
    check('orderId', 'Order ID is required').not().isEmpty()
  ],
  createPaymentIntent
);

router.post(
  '/confirm',
  authorize('buyer', 'admin'),
  [
    check('paymentIntentId', 'Payment intent ID is required').not().isEmpty(),
    check('orderId', 'Order ID is required').not().isEmpty()
  ],
  confirmPayment
);

router.post(
  '/create-bid-payment-intent',
  authorize('buyer'),
  [
    check('bidId', 'Bid ID is required').not().isEmpty(),
    check('amount', 'Amount is required').isFloat({ min: 0.01 })
  ],
  createBidPaymentIntent
);

router.post(
  '/complete-bid-purchase',
  authorize('buyer'),
  [
    check('bidId', 'Bid ID is required').not().isEmpty(),
    check('paymentIntentId', 'Payment intent ID is required').not().isEmpty()
  ],
  completeBidPurchase
);

// Seller routes
router.post('/create-connect-account', authorize('seller'), createConnectAccount);
router.get('/connect-account-status/:accountId', authorize('seller'), getConnectAccountStatus);
router.get('/connect-account-details/:accountId', authorize('seller'), getConnectAccountDetails);
router.post('/update-connect-account', authorize('seller'), updateConnectAccount);
router.post('/create-dashboard-link', authorize('seller'), createDashboardLink);
router.get('/seller', authorize('seller'), getSellerPayments);

// Admin routes
router.get('/', authorize('admin'), getPayments);
router.post('/:id/payout', authorize('admin'), processPayout);
router.post('/:orderId/complete', authorize('admin'), manualCompletePayment);

// Common routes
router.get('/:id', getPayment);

module.exports = router;
