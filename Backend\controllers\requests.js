const ErrorResponse = require('../utils/errorResponse');
const CustomRequest = require('../models/CustomRequest');
const User = require('../models/User');
const Content = require('../models/Content');
const { validationResult } = require('express-validator');

// @desc    Get all custom requests
// @route   GET /api/requests
// @access  Private/Admin
exports.getRequests = async (req, res, next) => {
  try {
    const requests = await CustomRequest.find()
      .populate({
        path: 'buyer',
        select: 'firstName lastName email'
      })
      .populate({
        path: 'seller',
        select: 'firstName lastName email'
      });

    res.status(200).json({
      success: true,
      count: requests.length,
      data: requests
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get single custom request
// @route   GET /api/requests/:id
// @access  Private
exports.getRequest = async (req, res, next) => {
  try {
    const request = await CustomRequest.findById(req.params.id)
      .populate({
        path: 'buyer',
        select: 'firstName lastName email'
      })
      .populate({
        path: 'seller',
        select: 'firstName lastName email'
      })
      .populate({
        path: 'relatedContent',
        select: 'title sport contentType'
      });

    if (!request) {
      return next(
        new ErrorResponse(`Custom request not found with id of ${req.params.id}`, 404)
      );
    }

    // Make sure user is request buyer or seller or admin
    if (
      request.buyer._id.toString() !== req.user.id &&
      request.seller._id.toString() !== req.user.id &&
      req.user.role !== 'admin'
    ) {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to view this request`,
          403
        )
      );
    }

    res.status(200).json({
      success: true,
      data: request
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Create new custom request
// @route   POST /api/requests
// @access  Private/Buyer
exports.createRequest = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    const { sellerId, relatedContentId, title, description, sport, contentType, requestedDeliveryDate, budget } = req.body;

    // Note: relatedContentId is optional for pure custom requests (new content creation)
    // It's only used when buyer wants custom content similar to existing content

    // Check if user is a buyer (use effective role for non-admin users)
    const effectiveRole = req.user.role === 'admin' ? req.user.role : req.user.activeRole;
    if (effectiveRole !== 'buyer' && req.user.role !== 'admin') {
      return next(
        new ErrorResponse(`User ${req.user.id} is not authorized to create a custom request`, 403)
      );
    }

    // Get seller
    const seller = await User.findById(sellerId);
    if (!seller) {
      return next(
        new ErrorResponse(`Seller not found with id of ${sellerId}`, 404)
      );
    }

    // Check if seller is verified
    if (!seller.isVerified) {
      return next(
        new ErrorResponse(`Seller is not verified`, 400)
      );
    }

    // Optional: Get related content if provided (for reference only)
    let relatedContent = null;
    if (relatedContentId) {
      const Content = require('../models/Content');
      relatedContent = await Content.findById(relatedContentId);
      if (!relatedContent) {
        return next(
          new ErrorResponse(`Related content not found with id of ${relatedContentId}`, 404)
        );
      }

      if (!relatedContent.allowCustomRequests) {
        return next(
          new ErrorResponse(`This content does not allow custom requests`, 400)
        );
      }

      // Verify the seller owns the content
      if (relatedContent.seller.toString() !== sellerId) {
        return next(
          new ErrorResponse(`Seller does not own the specified content`, 400)
        );
      }
    }

    // Create request
    const request = await CustomRequest.create({
      buyer: req.user.id,
      seller: sellerId,
      relatedContent: relatedContentId || null, // Optional reference content
      title,
      description,
      sport,
      contentType,
      requestedDeliveryDate,
      budget,
      status: 'Pending'
    });

    // Send notification to seller
    const Notification = require('../models/Notification');
    await Notification.create({
      user: sellerId,
      title: 'New Custom Request',
      message: `You have received a new custom request for "${title}"`,
      type: 'custom_request',
      relatedId: request._id,
      onModel: 'CustomRequest'
    });

    // Send asynchronous email notification to seller
    const sendEmail = require('../utils/sendEmail');

    sendEmail({
      to: seller.email,
      subject: 'New Custom Request - XO Sports Hub',
      message: `You have received a new custom request for "${title}". Please log in to your dashboard to review and respond.`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">New Custom Request</h2>
          <p>Hello ${seller.firstName || 'Seller'},</p>
          <p>You have received a new custom request:</p>
          <div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin: 0 0 10px 0;">${title}</h3>
            <p style="margin: 0 0 10px 0;"><strong>Budget:</strong> $${budget}</p>
            <p style="margin: 0;">${description.substring(0, 200)}${description.length > 200 ? '...' : ''}</p>
          </div>
          <p>Please log in to your seller dashboard to review and respond to this request.</p>
          <hr style="border: none; border-top: 1px solid #dee2e6; margin: 30px 0;">
          <p style="color: #6c757d; font-size: 12px; text-align: center;">
            This is an automated notification from XO Sports Hub.
          </p>
        </div>
      `
    }).then(() => {
      console.log(`New custom request notification sent to seller: ${seller.email}`);
    }).catch((emailError) => {
      console.error('Error sending new custom request notification to seller:', emailError);
    });

    res.status(201).json({
      success: true,
      data: request
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Respond to custom request
// @route   PUT /api/requests/:id/respond
// @access  Private/Seller
exports.respondToRequest = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    const { accepted, paymentType, price, estimatedDeliveryDate, message } = req.body;

    let request = await CustomRequest.findById(req.params.id)
      .populate('buyer', 'firstName lastName email')
      .populate('seller', 'firstName lastName email');

    if (!request) {
      return next(
        new ErrorResponse(`Custom request not found with id of ${req.params.id}`, 404)
      );
    }

    // Make sure user is request seller
    if (request.seller._id.toString() !== req.user.id) {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to respond to this request`,
          403
        )
      );
    }

    // Check if request is pending
    if (request.status !== 'Pending') {
      return next(
        new ErrorResponse(`Request is not in a pending state`, 400)
      );
    }

    // Update request with seller response
    request.sellerResponse = {
      accepted,
      paymentType: accepted ? paymentType || 'full' : undefined,
      price: accepted ? price : undefined,
      estimatedDeliveryDate: accepted ? estimatedDeliveryDate : undefined,
      message,
      responseDate: new Date()
    };

    if (accepted) {
      request.status = 'Accepted';

      // Set up payment details
      request.paymentDetails = {
        totalAmount: price,
        paidAmount: 0,
        remainingAmount: paymentType === 'half' ? price / 2 : 0,
        initialPaymentCompleted: false,
        finalPaymentCompleted: paymentType === 'full' ? false : false
      };

      // Create initial order for payment
      const Order = require('../models/Order');
      const Setting = require('../models/Setting');
      const settings = await Setting.getSingleton();

      const paymentAmount = paymentType === 'half' ? price / 2 : price;
      const platformFeePercentage = settings.financial?.platformCommissionPercentage || 5;
      const platformFee = (paymentAmount * platformFeePercentage) / 100;
      const sellerEarnings = paymentAmount - platformFee;

      const { getPaymentDeadline } = require('../config/timeouts');

      const order = await Order.create({
        buyer: request.buyer._id,
        seller: request.seller._id,
        content: null, // Custom orders are for NEW content creation, not existing content
        orderType: 'Custom',
        amount: paymentAmount,
        platformFee,
        sellerEarnings,
        totalAmount: paymentAmount,
        customRequestId: request._id,
        paymentDeadline: getPaymentDeadline(),
        status: 'Pending',
        paymentStatus: 'Pending'
      });

      console.log('Created order for custom request:', order._id);
      request.paymentDetails.initialOrderId = order._id;
      console.log('Set initialOrderId:', request.paymentDetails.initialOrderId);
    } else {
      request.status = 'Rejected';
    }

    await request.save();

    // Send notification to buyer
    const Notification = require('../models/Notification');
    const notificationTitle = accepted ? 'Custom Request Accepted' : 'Custom Request Rejected';
    const notificationMessage = accepted
      ? `Your custom request "${request.title}" has been accepted. Payment: $${price} (${paymentType === 'half' ? 'Half payment required' : 'Full payment required'})`
      : `Your custom request "${request.title}" has been rejected.`;

    await Notification.create({
      user: request.buyer._id,
      title: notificationTitle,
      message: notificationMessage,
      type: 'custom_request',
      relatedId: request._id,
      onModel: 'CustomRequest'
    });

    // Send asynchronous email notification to buyer
    const sendEmail = require('../utils/sendEmail');

    const emailSubject = accepted ? 'Custom Request Accepted - XO Sports Hub' : 'Custom Request Rejected - XO Sports Hub';
    const emailMessage = accepted
      ? `Your custom request "${request.title}" has been accepted by ${request.seller.firstName}. Please proceed with payment to start the work.`
      : `Your custom request "${request.title}" has been rejected by ${request.seller.firstName}.`;

    sendEmail({
      to: request.buyer.email,
      subject: emailSubject,
      message: emailMessage,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">${notificationTitle}</h2>
          <p>Hello ${request.buyer.firstName || 'Buyer'},</p>
          ${accepted ? `
            <p>Great news! Your custom request has been accepted:</p>
            <div style="background: #f0f9f0; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #28a745;">
              <h3 style="margin: 0 0 10px 0;">${request.title}</h3>
              <p style="margin: 0 0 10px 0;"><strong>Price:</strong> $${price}</p>
              <p style="margin: 0 0 10px 0;"><strong>Payment Type:</strong> ${paymentType === 'half' ? 'Half payment upfront' : 'Full payment upfront'}</p>
              <p style="margin: 0 0 10px 0;"><strong>Estimated Delivery:</strong> ${estimatedDeliveryDate ? new Date(estimatedDeliveryDate).toLocaleDateString() : 'TBD'}</p>
              ${message ? `<p style="margin: 0;"><strong>Message:</strong> ${message}</p>` : ''}
            </div>
            <p>Please log in to your dashboard to proceed with payment and start the work.</p>
          ` : `
            <p>Unfortunately, your custom request has been rejected:</p>
            <div style="background: #f8d7da; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #dc3545;">
              <h3 style="margin: 0 0 10px 0;">${request.title}</h3>
              ${message ? `<p style="margin: 0;"><strong>Reason:</strong> ${message}</p>` : ''}
            </div>
            <p>You can browse other strategies or contact the seller for more information.</p>
          `}
          <hr style="border: none; border-top: 1px solid #dee2e6; margin: 30px 0;">
          <p style="color: #6c757d; font-size: 12px; text-align: center;">
            This is an automated notification from XO Sports Hub.
          </p>
        </div>
      `
    }).then(() => {
      console.log(`Custom request response notification sent to buyer: ${request.buyer.email}`);
    }).catch((emailError) => {
      console.error('Error sending custom request response notification to buyer:', emailError);
    });

    res.status(200).json({
      success: true,
      data: request
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Cancel custom request
// @route   PUT /api/requests/:id/cancel
// @access  Private/Buyer
exports.cancelRequest = async (req, res, next) => {
  try {
    let request = await CustomRequest.findById(req.params.id);

    if (!request) {
      return next(
        new ErrorResponse(`Custom request not found with id of ${req.params.id}`, 404)
      );
    }

    // Make sure user is request buyer
    if (request.buyer.toString() !== req.user.id && req.user.role !== 'admin') {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to cancel this request`,
          403
        )
      );
    }

    // Check if request can be cancelled
    if (request.status !== 'Pending' && request.status !== 'Accepted') {
      return next(
        new ErrorResponse(`Request cannot be cancelled in its current state`, 400)
      );
    }

    // Update request
    request.status = 'Cancelled';
    await request.save();

    res.status(200).json({
      success: true,
      data: request
    });
  } catch (err) {
    next(err);
  }
};



// @desc    Get buyer requests
// @route   GET /api/requests/buyer
// @access  Private/Buyer
exports.getBuyerRequests = async (req, res, next) => {
  try {
    const requests = await CustomRequest.find({ buyer: req.user.id })
      .populate({
        path: 'seller',
        select: 'firstName lastName email'
      })
      .sort('-createdAt');

    res.status(200).json({
      success: true,
      count: requests.length,
      data: requests
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get seller requests
// @route   GET /api/requests/seller
// @access  Private/Seller
exports.getSellerRequests = async (req, res, next) => {
  try {
    const requests = await CustomRequest.find({ seller: req.user.id })
      .populate({
        path: 'buyer',
        select: 'firstName lastName email'
      })
      .sort('-createdAt');

    res.status(200).json({
      success: true,
      count: requests.length,
      data: requests
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Request remaining payment for custom request
// @route   PUT /api/requests/:id/request-remaining-payment
// @access  Private/Seller
exports.requestRemainingPayment = async (req, res, next) => {
  try {
    let request = await CustomRequest.findById(req.params.id)
      .populate('buyer', 'firstName lastName email')
      .populate('seller', 'firstName lastName email');

    if (!request) {
      return next(
        new ErrorResponse(`Custom request not found with id of ${req.params.id}`, 404)
      );
    }

    // Make sure user is request seller
    if (request.seller._id.toString() !== req.user.id) {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to request payment for this request`,
          403
        )
      );
    }

    // Check if request is in progress and initial payment is completed
    if (request.status !== 'In Progress' || !request.paymentDetails.initialPaymentCompleted) {
      return next(
        new ErrorResponse(`Cannot request remaining payment for request with current status`, 400)
      );
    }

    // Check if remaining payment was already requested
    if (request.remainingPaymentRequested) {
      return next(
        new ErrorResponse(`Remaining payment has already been requested`, 400)
      );
    }

    // Check if there's remaining amount to pay
    if (request.paymentDetails.remainingAmount <= 0) {
      return next(
        new ErrorResponse(`No remaining payment required`, 400)
      );
    }

    // Create order for remaining payment
    const Order = require('../models/Order');
    const Setting = require('../models/Setting');
    const settings = await Setting.getSingleton();

    const paymentAmount = request.paymentDetails.remainingAmount;
    const platformFeePercentage = settings.financial?.platformCommissionPercentage || 5;
    const platformFee = (paymentAmount * platformFeePercentage) / 100;
    const sellerEarnings = paymentAmount - platformFee;

    const { getPaymentDeadline } = require('../config/timeouts');

    const order = await Order.create({
      buyer: request.buyer._id,
      seller: request.seller._id,
      content: null, // Custom orders are for NEW content creation, not existing content
      orderType: 'Custom',
      amount: paymentAmount,
      platformFee,
      sellerEarnings,
      totalAmount: paymentAmount,
      customRequestId: request._id,
      paymentDeadline: getPaymentDeadline(),
      status: 'Pending',
      paymentStatus: 'Pending'
    });

    // Update request
    request.paymentDetails.finalOrderId = order._id;
    request.remainingPaymentRequested = true;
    request.remainingPaymentRequestedAt = new Date();
    await request.save();

    // Send notification to buyer
    const Notification = require('../models/Notification');
    await Notification.create({
      user: request.buyer._id,
      title: 'Remaining Payment Required',
      message: `The seller has completed your custom request "${request.title}" and is requesting the remaining payment of $${paymentAmount}`,
      type: 'custom_request',
      relatedId: request._id,
      onModel: 'CustomRequest'
    });

    // Send asynchronous email notification to buyer about remaining payment request
    const sendEmail = require('../utils/sendEmail');

    sendEmail({
      to: request.buyer.email,
      subject: 'Remaining Payment Required - Custom Request - XO Sports Hub',
      message: `Your custom request "${request.title}" is ready! The seller is requesting the remaining payment of $${paymentAmount} to complete the transaction.`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">Remaining Payment Required</h2>
          <p>Hello ${request.buyer.firstName || 'Buyer'},</p>
          <p>Great news! Your custom request is ready and the seller is requesting the remaining payment:</p>
          <div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ffc107;">
            <h3 style="margin: 0 0 10px 0;">${request.title}</h3>
            <p style="margin: 0 0 10px 0;"><strong>Remaining Payment:</strong> $${paymentAmount}</p>
            <p style="margin: 0 0 10px 0;"><strong>Seller:</strong> ${request.seller.firstName} ${request.seller.lastName}</p>
            <p style="margin: 0 0 10px 0;"><strong>Total Project Cost:</strong> $${request.sellerResponse.price}</p>
          </div>
          <p>The seller has completed your custom content and is ready to deliver it once the remaining payment is processed.</p>
          <p>Please log in to your dashboard to complete the payment and access your custom content.</p>
          <hr style="border: none; border-top: 1px solid #dee2e6; margin: 30px 0;">
          <p style="color: #6c757d; font-size: 12px; text-align: center;">
            This is an automated notification from XO Sports Hub.
          </p>
        </div>
      `
    }).then(() => {
      console.log(`Remaining payment request notification sent to buyer: ${request.buyer.email}`);
    }).catch((emailError) => {
      console.error('Error sending remaining payment request notification to buyer:', emailError);
    });

    res.status(200).json({
      success: true,
      data: request
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Submit content for custom request
// @route   PUT /api/requests/:id/submit-content
// @access  Private/Seller
exports.submitCustomContent = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    const { contentId, submissionMessage } = req.body;

    let request = await CustomRequest.findById(req.params.id)
      .populate('buyer', 'firstName lastName email')
      .populate('seller', 'firstName lastName email');

    if (!request) {
      return next(
        new ErrorResponse(`Custom request not found with id of ${req.params.id}`, 404)
      );
    }

    // Make sure user is request seller
    if (request.seller._id.toString() !== req.user.id) {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to submit content for this request`,
          403
        )
      );
    }

    // Check if request is in the right status
    if (!['In Progress', 'Accepted'].includes(request.status)) {
      return next(
        new ErrorResponse(`Cannot submit content for request with status ${request.status}`, 400)
      );
    }

    // Verify the content exists and belongs to the seller
    const Content = require('../models/Content');
    const content = await Content.findById(contentId);
    if (!content) {
      return next(
        new ErrorResponse(`Content not found with id of ${contentId}`, 404)
      );
    }

    if (content.seller.toString() !== req.user.id) {
      return next(
        new ErrorResponse(`You are not authorized to submit this content`, 403)
      );
    }

    // Add new content submission to the array
    const newSubmission = {
      submittedAt: new Date(),
      contentId: contentId,
      submissionMessage: submissionMessage || 'Content has been submitted for your custom request.',
      isActive: true
    };

    // Initialize contentSubmissions array if it doesn't exist
    if (!request.contentSubmissions) {
      request.contentSubmissions = [];
    }

    // Add the new submission to the array
    request.contentSubmissions.push(newSubmission);

    // Update the main contentSubmission for backward compatibility
    request.contentSubmission = {
      isSubmitted: true,
      submittedAt: new Date(),
      contentId: contentId,
      submissionMessage: submissionMessage || 'Content has been submitted for your custom request.'
    };

    // Update status based on payment completion
    if (request.sellerResponse.paymentType === 'full' && request.paymentDetails.initialPaymentCompleted) {
      request.status = 'Completed';
      request.paymentDetails.finalPaymentCompleted = true;
    } else if (request.sellerResponse.paymentType === 'half' && request.paymentDetails.finalPaymentCompleted) {
      request.status = 'Completed';
    } else {
      request.status = 'Content Submitted';
    }

    await request.save();

    // Send notification to buyer based on payment type
    const Notification = require('../models/Notification');

    let notificationTitle, notificationMessage;

    if (request.sellerResponse.paymentType === 'half' && !request.paymentDetails.finalPaymentCompleted) {
      // Half payment - buyer needs to pay remaining amount
      notificationTitle = 'Custom Content Ready - Payment Required';
      notificationMessage = `Your custom content "${request.title}" is ready! Please complete the remaining payment of $${request.paymentDetails.remainingAmount} to access your content.`;
    } else {
      // Full payment or final payment completed - content is accessible
      notificationTitle = 'Custom Content Delivered';
      notificationMessage = `Your custom request "${request.title}" has been completed and the content is now available for download.`;
    }

    await Notification.create({
      user: request.buyer._id,
      title: notificationTitle,
      message: notificationMessage,
      type: 'custom_request',
      relatedId: request._id,
      onModel: 'CustomRequest'
    });

    // Send asynchronous email notification to buyer
    const sendEmail = require('../utils/sendEmail');

    sendEmail({
      to: request.buyer.email,
      subject: 'Custom Content Delivered - XO Sports Hub',
      message: `Your custom request "${request.title}" has been completed and the content is now available.`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">Custom Content Delivered</h2>
          <p>Hello ${request.buyer.firstName || 'Buyer'},</p>
          <p>Excellent news! Your custom request has been completed:</p>
          <div style="background: #f0f9f0; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #28a745;">
            <h3 style="margin: 0 0 10px 0;">${request.title}</h3>
            <p style="margin: 0 0 10px 0;"><strong>Status:</strong> ${request.status}</p>
            ${submissionMessage ? `<p style="margin: 0;"><strong>Message from Seller:</strong> ${submissionMessage}</p>` : ''}
          </div>
          <p>You can now access your custom content from your dashboard.</p>
          <hr style="border: none; border-top: 1px solid #dee2e6; margin: 30px 0;">
          <p style="color: #6c757d; font-size: 12px; text-align: center;">
            This is an automated notification from XO Sports Hub.
          </p>
        </div>
      `
    }).then(() => {
      console.log(`Custom content delivery notification sent to buyer: ${request.buyer.email}`);
    }).catch((emailError) => {
      console.error('Error sending custom content delivery notification to buyer:', emailError);
    });

    res.status(200).json({
      success: true,
      data: request
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get buyer's custom content
// @route   GET /api/requests/buyer/content
// @access  Private/Buyer
exports.getBuyerCustomContent = async (req, res, next) => {
  try {
    console.log(`[DEBUG] Getting custom content for buyer: ${req.user.id}`);

    // Find completed custom requests for the buyer with proper payment validation
    const completedRequests = await CustomRequest.find({
      buyer: req.user.id,
      status: 'Completed',
      'contentSubmission.isSubmitted': true,
      // Ensure payment is completed based on payment type
      $or: [
        // Full payment scenario - initial payment completed
        {
          'sellerResponse.paymentType': 'full',
          'paymentDetails.initialPaymentCompleted': true
        },
        // Half payment scenario - both initial and final payments completed
        {
          'sellerResponse.paymentType': 'half',
          'paymentDetails.initialPaymentCompleted': true,
          'paymentDetails.finalPaymentCompleted': true
        }
      ]
    }).populate('contentSubmission.contentId')
      .populate('contentSubmissions.contentId')
      .populate('relatedContent');

    console.log(`[DEBUG] Found ${completedRequests.length} completed requests`);

    // Extract content from completed requests with detailed logging
    const customContent = [];

    for (const request of completedRequests) {
      console.log(`[DEBUG] Processing request ${request._id}: title="${request.title}"`);

      // Get all content submissions for this request
      const submissions = request.contentSubmissions || [];
      const hasMainContent = !!request.contentSubmission?.contentId;

      console.log(`[DEBUG] Request has ${submissions.length} submissions and main content: ${hasMainContent}`);

      // Add all submissions from the contentSubmissions array
      for (const submission of submissions) {
        if (submission.contentId && submission.isActive) {
          const content = submission.contentId.toObject();
          console.log(`[DEBUG] Adding submission content: ${content._id}, title: "${content.title}"`);

          customContent.push({
            ...content,
            customRequestId: request._id,
            requestTitle: request.title,
            completedAt: submission.submittedAt,
            paymentType: request.sellerResponse.paymentType,
            totalPaid: request.paymentDetails.paidAmount,
            submissionMessage: submission.submissionMessage,
            // Add debug info
            _debug: {
              isActualCustomContent: content.isCustomContent,
              customRequestId: content.customRequestId,
              relatedContentId: request.relatedContent?._id || null,
              relatedContentTitle: request.relatedContent?.title || null,
              submissionType: 'array'
            }
          });
        }
      }

      // Also add the main content submission if it exists and is not already in the array
      if (hasMainContent) {
        const mainContent = request.contentSubmission.contentId.toObject();
        const alreadyAdded = submissions.some(sub =>
          sub.contentId && sub.contentId._id.toString() === mainContent._id.toString()
        );

        if (!alreadyAdded) {
          console.log(`[DEBUG] Adding main content: ${mainContent._id}, title: "${mainContent.title}"`);

          customContent.push({
            ...mainContent,
            customRequestId: request._id,
            requestTitle: request.title,
            completedAt: request.contentSubmission.submittedAt,
            paymentType: request.sellerResponse.paymentType,
            totalPaid: request.paymentDetails.paidAmount,
            submissionMessage: request.contentSubmission.submissionMessage,
            // Add debug info
            _debug: {
              isActualCustomContent: mainContent.isCustomContent,
              customRequestId: mainContent.customRequestId,
              relatedContentId: request.relatedContent?._id || null,
              relatedContentTitle: request.relatedContent?.title || null,
              submissionType: 'main'
            }
          });
        }
      }
    }

    console.log(`[DEBUG] Returning ${customContent.length} custom content items`);

    res.status(200).json({
      success: true,
      count: customContent.length,
      data: customContent
    });
  } catch (err) {
    console.error('[DEBUG] Error in getBuyerCustomContent:', err);
    next(err);
  }
};

// @desc    Debug buyer's custom content flow
// @route   GET /api/requests/buyer/content/debug
// @access  Private/Buyer
exports.debugBuyerCustomContent = async (req, res, next) => {
  try {
    const buyerId = req.user.id;
    console.log(`[DEBUG] Debugging custom content flow for buyer: ${buyerId}`);

    // Get all custom requests for this buyer
    const allRequests = await CustomRequest.find({ buyer: buyerId })
      .populate('contentSubmission.contentId')
      .populate('contentSubmissions.contentId')
      .populate('relatedContent')
      .populate('seller', 'firstName lastName email')
      .sort({ createdAt: -1 });

    const debugInfo = {
      buyerId,
      totalRequests: allRequests.length,
      requests: allRequests.map(request => ({
        requestId: request._id,
        title: request.title,
        status: request.status,
        paymentType: request.sellerResponse?.paymentType,
        initialPaymentCompleted: request.paymentDetails?.initialPaymentCompleted,
        finalPaymentCompleted: request.paymentDetails?.finalPaymentCompleted,
        contentSubmitted: request.contentSubmission?.isSubmitted,
        submittedAt: request.contentSubmission?.submittedAt,
        deliveredContentId: request.contentSubmission?.contentId?._id,
        deliveredContentTitle: request.contentSubmission?.contentId?.title,
        deliveredContentIsCustom: request.contentSubmission?.contentId?.isCustomContent,
        // New: Show all content submissions
        totalSubmissions: request.contentSubmissions?.length || 0,
        allSubmissions: request.contentSubmissions?.map(sub => ({
          contentId: sub.contentId?._id,
          contentTitle: sub.contentId?.title,
          submittedAt: sub.submittedAt,
          isActive: sub.isActive,
          submissionMessage: sub.submissionMessage
        })) || [],
        relatedContentId: request.relatedContent?._id,
        relatedContentTitle: request.relatedContent?.title,
        seller: request.seller ? `${request.seller.firstName} ${request.seller.lastName}` : 'Unknown',
        createdAt: request.createdAt
      }))
    };

    res.status(200).json({
      success: true,
      debug: debugInfo
    });
  } catch (err) {
    console.error('[DEBUG] Error in debugBuyerCustomContent:', err);
    next(err);
  }
};



// @desc    Create missing payment order for custom request
// @route   POST /api/requests/:id/create-payment-order
// @access  Private/Buyer
exports.createPaymentOrder = async (req, res, next) => {
  try {
    const request = await CustomRequest.findById(req.params.id)
      .populate('buyer', 'firstName lastName email')
      .populate('seller', 'firstName lastName email paymentInfo');

    if (!request) {
      return next(
        new ErrorResponse(`Custom request not found with id of ${req.params.id}`, 404)
      );
    }

    // Make sure user is request buyer
    if (request.buyer._id.toString() !== req.user.id) {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to create payment order for this request`,
          403
        )
      );
    }

    // Check if request is accepted
    if (request.status !== 'Accepted') {
      return next(
        new ErrorResponse('Request must be accepted to create payment order', 400)
      );
    }

    // Check if order already exists and is valid
    if (request.paymentDetails.initialOrderId) {
      try {
        const Order = require('../models/Order');
        const existingOrder = await Order.findById(request.paymentDetails.initialOrderId);
        if (existingOrder) {
          return res.status(200).json({
            success: true,
            data: {
              orderId: existingOrder._id,
              amount: existingOrder.amount
            },
            message: 'Payment order already exists'
          });
        } else {
          console.log('Order ID exists in request but order not found, recreating...');
          // Continue to create new order
        }
      } catch (orderCheckError) {
        console.error('Error checking existing order:', orderCheckError);
        // Continue to create new order
      }
    }

    // Calculate payment amount (initial payment)
    const paymentAmount = request.sellerResponse.paymentType === 'full'
      ? request.sellerResponse.price
      : request.sellerResponse.price / 2;

    // Calculate platform fee and seller earnings
    const SiteSetting = require('../models/SiteSetting');
    const platformCommissionSetting = await SiteSetting.findOne({ key: 'platformCommission' });
    const platformCommissionRate = platformCommissionSetting ?
      parseFloat(platformCommissionSetting.value) / 100 : 0.05;

    const platformFee = Math.round(paymentAmount * platformCommissionRate * 100) / 100;
    const sellerEarnings = paymentAmount - platformFee;

    const { getPaymentDeadline } = require('../config/timeouts');
    const Order = require('../models/Order');

    const order = await Order.create({
      buyer: request.buyer._id,
      seller: request.seller._id,
      content: null, // Custom orders are for NEW content creation, not existing content
      orderType: 'Custom',
      amount: paymentAmount,
      platformFee,
      sellerEarnings,
      totalAmount: paymentAmount,
      customRequestId: request._id,
      paymentDeadline: getPaymentDeadline(),
      status: 'Pending',
      paymentStatus: 'Pending'
    });

    // Update request with order ID
    request.paymentDetails.initialOrderId = order._id;
    await request.save();

    res.status(200).json({
      success: true,
      data: {
        orderId: order._id,
        amount: paymentAmount
      }
    });
  } catch (err) {
    next(err);
  }
};
