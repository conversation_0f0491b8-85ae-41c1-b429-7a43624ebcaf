/* Role Selection Modal Styles */
.role-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}
.role-modal-overlay .role-modal {
  background: white;
  border-radius: 16px;
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.role-modal-overlay .role-modal__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 24px;
}

.role-modal-overlay .role-modal__title {
  font-size: 24px;
  font-weight: 700;
  color: var(--secondary-color);
  margin: 0;
}

.role-modal-overlay .role-modal__close {
  background: none;
  border: none;
  font-size: 20px;
  color: #6b7280;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.role-modal-overlay .role-modal__close:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.role-modal-overlay .role-modal__close:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.role-modal-overlay .role-modal__content {
  padding: 0 24px 24px;
}

.role-modal-overlay .role-modal__user-info {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background-color: #f8fafc;
  border-radius: 12px;
  margin-bottom: 24px;
}

.role-modal-overlay .role-modal__avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.role-modal-overlay .role-modal__user-details {
  flex: 1;
}

.role-modal-overlay .role-modal__user-name {
  font-size: 18px;
  font-weight: 600;
  color: var(--secondary-color);
  margin: 0 0 4px 0;
}

.role-modal-overlay .role-modal__user-email {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.role-modal-overlay .role-modal__role-selection {
  margin-bottom: 32px;
}

.role-modal-overlay .role-modal__label {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16px;
}

.role-modal-overlay .role-modal__options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.role-modal-overlay .role-modal__option {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.role-modal-overlay .role-modal__option:hover {
  border-color: #d1d5db;
  background-color: #f9fafb;
}

.role-modal-overlay .role-modal__option--selected {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.role-modal-overlay .role-modal__option-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background-color: #3b82f6;
  color: white;
  border-radius: 12px;
  font-size: 20px;
  flex-shrink: 0;
}

.role-modal-overlay .role-modal__option--selected .role-modal__option-icon {
  background-color: #1d4ed8;
}

.role-modal-overlay .role-modal__option-content {
  flex: 1;
}

.role-modal-overlay .role-modal__option-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--secondary-color);
  margin: 0 0 4px 0;
}

.role-modal-overlay .role-modal__option-description {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
  line-height: 1.4;
}

.role-modal-overlay .role-modal__check-circle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background-color: #10b981;
  border-radius: 50%;
  color: white;
  font-size: 12px;
  flex-shrink: 0;
}

.role-modal-overlay .role-modal__actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.role-modal-overlay .role-modal__button {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  min-width: 120px;
}

.role-modal-overlay .role-modal__button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.role-modal-overlay .role-modal__button--secondary {
  background-color: #f3f4f6;
  color: #374151;
}

.role-modal-overlay .role-modal__button--secondary:hover:not(:disabled) {
  background-color: #e5e7eb;
}

.role-modal-overlay .role-modal__button--primary {
  background-color: #3b82f6;
  color: white;
}

.role-modal__button--primary:hover:not(:disabled) {
  background-color:var(--btn-color);
}

/* Responsive Design */
@media (max-width: 640px) {
  .role-modal-overlay .role-modal {
    width: 95%;
    max-height: 95vh;
    margin: 20px;
  }

  .role-modal-overlay .role-modal__header,
  .role-modal-overlay .role-modal__content {
    padding-left: 16px;
    padding-right: 16px;
  }

  .role-modal-overlay .role-modal__title {
    font-size: 20px;
  }

  .role-modal-overlay .role-modal__user-info {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .role-modal-overlay .role-modal__actions {
    flex-direction: column;
  }

  .role-modal-overlay .role-modal__button {
    width: 100%;
  }
}
