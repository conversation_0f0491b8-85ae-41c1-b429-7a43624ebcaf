import api from './api';
import { DASHBOARD_ENDPOINTS, ORDER_ENDPOINTS, BID_ENDPOINTS, REQUEST_ENDPOINTS } from '../utils/constants';

/**
 * Dashboard Service for Buyer-specific data
 */

/**
 * Get buyer dashboard stats
 * @returns {Promise} Promise with dashboard stats
 */
export const getBuyerDashboardStats = async () => {
  try {
    const response = await api.get(`${DASHBOARD_ENDPOINTS.STATS}/buyer`);
    return response.data?.data || response.data;
  } catch (error) {
    console.error('Error fetching buyer dashboard stats:', error);
    // Return default stats if the endpoint fails
    return {
      totalDownloads: 0,
      totalRequests: 0,
      totalBids: 0,
      totalSpent: 0,
      activeOrders: 0,
      completedOrders: 0
    };
  }
};

/**
 * Get buyer orders with pagination and filtering
 * @param {Object} params - Query parameters
 * @returns {Promise} Promise with orders data
 */
export const getBuyerOrders = async (params = {}) => {
  try {
    const response = await api.get(ORDER_ENDPOINTS.BUYER_ORDERS, { params });
    return response.data;
  } catch (error) {
    console.error('Error fetching buyer orders:', error);
    throw error;
  }
};

/**
 * Get buyer downloads (completed orders with purchased content)
 * @param {Object} params - Query parameters including pagination (page, limit)
 * @returns {Promise} Promise with downloads data and pagination info
 */
export const getBuyerDownloads = async (params = {}) => {
  try {
    const response = await api.get(ORDER_ENDPOINTS.BUYER_DOWNLOADS, {
      params: {
        page: params.page || 1,
        limit: params.limit || 10,
        ...params
      },
      headers: {
        Authorization: `Bearer ${localStorage.getItem('xosportshub_token')}`
      }
    });

    const { data = [], count = 0 } = response.data;
    const currentPage = parseInt(params.page) || 1;
    const itemsPerPage = parseInt(params.limit) || 10;
    const totalPages = Math.ceil(count / itemsPerPage);

    // Format downloads data
    const formattedDownloads = data.map(download => ({
      _id: download._id,
      orderId: download.orderId,
      title: download.title,
      coach: download.coach,
      downloadDate: download.downloadDate,
      amount: download.amount,
      fileSize: download.fileSize,
      fileType: download.fileType,
      fileUrl: download.fileUrl,
      thumbnailUrl: download.thumbnailUrl,
      downloadCount: download.downloadCount,
      lastDownloaded: download.lastDownloaded,
      status: download.status,
      paymentStatus: download.paymentStatus,
      duration: download.duration,
      sport: download.sport,
      category: download.category,
      difficulty: download.difficulty,
      content: download.content
    }));

    // Return both the formatted data and pagination info
    return {
      data: formattedDownloads,
      currentPage,
      totalPages,
      totalItems: count,
      itemsPerPage
    };
  } catch (error) {
    console.error('Error fetching buyer downloads:', error);
    throw error;
  }
};

/**
 * Get buyer custom content (completed custom requests)
 * @param {Object} params - Query parameters
 * @returns {Promise} Promise with custom content data
 */
export const getBuyerCustomContent = async (params = {}) => {
  try {
    const response = await api.get('/requests/buyer/content', {
      params,
      headers: {
        Authorization: `Bearer ${localStorage.getItem('xosportshub_token')}`
      }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching buyer custom content:', error);
    throw error;
  }
};

/**
 * Get buyer bids
 * @param {Object} params - Query parameters
 * @returns {Promise} Promise with bids data
 */
export const getBuyerBids = async (params = {}) => {
  try {
    const response = await api.get(BID_ENDPOINTS.USER_BIDS, {
      params,
      headers: {
        Authorization: `Bearer ${localStorage.getItem('xosportshub_token')}`
      }
    });
    return Array.isArray(response.data) ? response.data : [];
  } catch (error) {
    console.error('Error fetching buyer bids:', error);
    return [];
  }
};

/**
 * Get buyer requests
 * @param {Object} params - Query parameters
 * @returns {Promise} Promise with requests data
 */
export const getBuyerRequests = async (params = {}) => {
  try {
    const response = await api.get(REQUEST_ENDPOINTS.BUYER_REQUESTS, { params });
    return response.data;
  } catch (error) {
    console.error('Error fetching buyer requests:', error);
    throw error;
  }
};

/**
 * Get buyer activity feed
 * @param {Object} params - Query parameters
 * @returns {Promise} Promise with activity data
 */
export const getBuyerActivity = async (params = {}) => {
  try {
    const response = await api.get(`${DASHBOARD_ENDPOINTS.ACTIVITY}/buyer`, { params });
    return response.data;
  } catch (error) {
    console.error('Error fetching buyer activity:', error);
    throw error;
  }
};

/**
 * Get buyer notifications
 * @returns {Promise} Promise with notifications data
 */
export const getBuyerNotifications = async () => {
  try {
    const response = await api.get('/notifications/me');
    return response.data;
  } catch (error) {
    console.error('Error fetching buyer notifications:', error);
    throw error;
  }
};

/**
 * Mark notification as read
 * @param {string} notificationId - Notification ID
 * @returns {Promise} Promise with success response
 */
export const markNotificationAsRead = async (notificationId) => {
  try {
    const response = await api.put(`/notifications/${notificationId}/read`);
    return response.data;
  } catch (error) {
    console.error('Error marking notification as read:', error);
    throw error;
  }
};

/**
 * Get buyer wishlist
 * @returns {Promise} Promise with wishlist data
 */
export const getBuyerWishlist = async () => {
  try {
    const response = await api.get('/wishlist');
    return response.data;
  } catch (error) {
    console.error('Error fetching buyer wishlist:', error);
    throw error;
  }
};

/**
 * Dashboard Service for Seller-specific data
 */

/**
 * Get seller dashboard stats
 * @returns {Promise} Promise with dashboard stats
 */
export const getSellerDashboardStats = async () => {
  try {
    const response = await api.get(`${DASHBOARD_ENDPOINTS.STATS}/seller`);
    return response.data?.data || response.data;
  } catch (error) {
    console.error('Error fetching seller dashboard stats:', error);
    // Return default stats if the endpoint fails
    return {
      totalStrategies: 0,
      totalRequests: 0,
      totalBids: 0,
      totalRevenue: 0,
      activeContent: 0,
      pendingOrders: 0
    };
  }
};

/**
 * Get seller requests
 * @param {Object} params - Query parameters
 * @returns {Promise} Promise with seller requests data
 */
export const getSellerRequests = async (params = {}) => {
  try {
    const response = await api.get(REQUEST_ENDPOINTS.SELLER_REQUESTS, {
      params,
      headers: {
        Authorization: `Bearer ${localStorage.getItem('xosportshub_token')}`
      }
    });
    return response.data?.data || response.data || [];
  } catch (error) {
    console.error('Error fetching seller requests:', error);
    throw error;
  }
};

/**
 * Get seller bids (bids on seller's content)
 * @param {Object} params - Query parameters
 * @returns {Promise} Promise with seller bids data
 */
export const getSellerBids = async (params = {}) => {
  try {
    const response = await api.get(BID_ENDPOINTS.SELLER_BIDS, {
      params,
      headers: {
        Authorization: `Bearer ${localStorage.getItem('xosportshub_token')}`
      }
    });
    return response.data?.data || response.data || [];
  } catch (error) {
    console.error('Error fetching seller bids:', error);
    throw error;
  }
};

/**
 * Get seller orders
 * @param {Object} params - Query parameters
 * @returns {Promise} Promise with seller orders data
 */
export const getSellerOrders = async (params = {}) => {
  try {
    const response = await api.get(ORDER_ENDPOINTS.SELLER_ORDERS, {
      params,
      headers: {
        Authorization: `Bearer ${localStorage.getItem('xosportshub_token')}`
      }
    });
    return response.data?.data || response.data || [];
  } catch (error) {
    console.error('Error fetching seller orders:', error);
    throw error;
  }
};

export default {
  getBuyerDashboardStats,
  getBuyerOrders,
  getBuyerDownloads,
  getBuyerCustomContent,
  getBuyerBids,
  getBuyerRequests,
  getBuyerActivity,
  getBuyerNotifications,
  markNotificationAsRead,
  getBuyerWishlist,
  // Seller methods
  getSellerDashboardStats,
  getSellerRequests,
  getSellerBids,
  getSellerOrders,
};
