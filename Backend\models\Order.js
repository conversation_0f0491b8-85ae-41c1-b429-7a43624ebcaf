const mongoose = require("mongoose");

const OrderSchema = new mongoose.Schema({
  buyer: {
    type: mongoose.Schema.ObjectId,
    ref: "User",
    required: true,
  },
  seller: {
    type: mongoose.Schema.ObjectId,
    ref: "User",
    required: true,
  },
  content: {
    type: mongoose.Schema.ObjectId,
    ref: "Content",
    required: false, // Optional for custom orders (new content creation)
  },
  orderType: {
    type: String,
    enum: ["Fixed", "Auction", "Custom"],
    required: true,
  },
  amount: {
    type: Number,
    required: [true, "Please add the order amount"],
  },
  platformFee: {
    type: Number,
    required: [true, "Please add the platform fee"],
  },
  sellerEarnings: {
    type: Number,
    required: [true, "Please add the seller earnings"],
  },
  totalAmount: {
    type: Number,
    default: function () {
      return this.amount || 0;
    },
  },
  paymentStatus: {
    type: String,
    enum: ["Pending", "Completed", "Failed", "Refunded", "Expired"],
    default: "Pending",
  },
  paymentDeadline: {
    type: Date,
    default: function () {
      const { getPaymentDeadline } = require('../config/timeouts');
      return getPaymentDeadline();
    }
  },
  expiredAt: {
    type: Date,
  },
  paymentIntentId: {
    type: String,
  },
  paymentMethod: {
    type: String,
  },
  // Card details used for this order payment
  cardDetails: {
    cardType: {
      type: String,
      enum: [
        "visa",
        "mastercard",
        "amex",
        "discover",
        "diners",
        "jcb",
        "unionpay",
        "unknown",
      ],
    },
    lastFourDigits: {
      type: String,
      length: 4,
    },
  },
  invoiceUrl: {
    type: String,
  },
  bidId: {
    type: mongoose.Schema.ObjectId,
    ref: "Bid",
  },
  customRequestId: {
    type: mongoose.Schema.ObjectId,
    ref: "CustomRequest",
  },
  status: {
    type: String,
    enum: ["Pending", "Processing", "Completed", "Cancelled", "Refunded"],
    default: "Pending",
  },
  downloadCount: {
    type: Number,
    default: 0,
  },
  lastDownloaded: {
    type: Date,
  },
  runnerUpNotified: {
    type: Boolean,
    default: false,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

// Custom validation to ensure either content or customRequestId is provided
OrderSchema.pre('validate', function(next) {
  // For custom orders, customRequestId is required and content is optional
  // For regular orders, content is required and customRequestId is optional
  if (this.orderType === 'Custom') {
    if (!this.customRequestId) {
      this.invalidate('customRequestId', 'Custom orders must have a customRequestId');
    }
  } else {
    if (!this.content) {
      this.invalidate('content', 'Regular orders must have a content reference');
    }
  }
  next();
});

// Indexes for efficient queries
OrderSchema.index({ buyer: 1, status: 1 });
OrderSchema.index({ seller: 1, status: 1 });
OrderSchema.index({ content: 1 });
OrderSchema.index({ paymentStatus: 1, paymentDeadline: 1 });
OrderSchema.index({ bidId: 1 });
OrderSchema.index({ customRequestId: 1 });

module.exports = mongoose.model("Order", OrderSchema);
